# src/services/notification_service.py
import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional

from src.database.redis_client import RedisClient

logger = logging.getLogger(__name__)

class NotificationService:
    def __init__(self, redis_client: RedisClient, task_ttl: int = 3600):
        self.redis_client = redis_client
        self.task_ttl = task_ttl

    async def _publish(self, task_id: str, event_data: Dict[str, Any]):
        # 确保Redis连接
        await self.redis_client.connect()

        channel = f"task_channel:{task_id}"
        message = json.dumps(event_data, ensure_ascii=False)
        await self.redis_client._redis.publish(channel, message)

    async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
        # 确保Redis连接
        await self.redis_client.connect()

        key = f"task_status:{task_id}"
        updates["last_updated"] = datetime.utcnow().isoformat()
        await self.redis_client._redis.hset(key, mapping=updates)
        # 每次更新都刷新TTL，防止正常任务过期
        await self.redis_client._redis.expire(key, self.task_ttl)

    async def initialize_task(self, task_id: str):
        """初始化任务状态并设置TTL。"""
        initial_state = {
            "overall_status": "pending",
            "current_step": "initialization",
            "steps_status": json.dumps({})
        }
        await self._update_task_status(task_id, initial_state)
        logger.info(f"Task {task_id} state initialized with TTL: {self.task_ttl}s.")

    async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
        event = {"event": "step_start", "data": {"step_id": f"{step_name}_{task_id}", "step_name": step_name, "title": title, "message": message}}
        await self._publish(task_id, event)
        
        status_update = {
            "overall_status": "running",
            "current_step": step_name
        }
        # 更新步骤状态
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis_client._redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        steps_status[step_name] = {"status": "running", "start_time": datetime.utcnow().isoformat()}
        status_update["steps_status"] = json.dumps(steps_status)
        
        await self._update_task_status(task_id, status_update)

    async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
        event = {"event": "step_end", "data": {"step_id": f"{step_name}_{task_id}", "step_name": step_name, "status": status, "result": result or {}}}
        await self._publish(task_id, event)
        
        key = f"task_status:{task_id}"
        steps_status_raw = await self.redis_client._redis.hget(key, "steps_status")
        steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
        if step_name in steps_status:
            steps_status[step_name]["status"] = status
            steps_status[step_name]["end_time"] = datetime.utcnow().isoformat()
        
        await self._update_task_status(task_id, {"steps_status": json.dumps(steps_status)})

    async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
        event = {"event": "complete", "data": final_data}
        await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos"})
        
        status_update = {
            "overall_status": "completed",
            "current_step": "finished",
            "final_result": json.dumps(final_data)
        }
        await self._update_task_status(task_id, status_update)

    async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
        event = {"event": "error", "data": {"step_name": step_name, "message": error_message}}
        await self._publish(task_id, event)
        await self._publish(task_id, {"event": "eos"})
        
        status_update = {
            "overall_status": "failed",
            "current_step": step_name,
            "error_info": error_message
        }
        await self._update_task_status(task_id, status_update)
