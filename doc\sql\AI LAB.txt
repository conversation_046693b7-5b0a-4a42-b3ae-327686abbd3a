-- =================================================================
-- Digital Human - AI Lab & VPA Center Schema
-- Version: Final
-- =================================================================

-- -----------------------------------------------------
-- Schema: dh_ailab
-- 描述: 负责管理 AI Lab 的所有业务功能，包括技能商城和 VPA 形象的生成与配置。
-- -----------------------------------------------------

-- 如果 Schema 不存在，则创建
CREATE SCHEMA IF NOT EXISTS `dh_ailab` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到目标 Schema
USE `dh_ailab`;

-- -----------------------------------------------------
-- 步骤 1: 按逆向依赖顺序，安全删除已存在的旧表
-- -----------------------------------------------------
DROP TABLE IF EXISTS `user_unlocked_skills`;
DROP TABLE IF EXISTS `user_vpa_instances`;
DROP TABLE IF EXISTS `ai_skills`;
DROP TABLE IF EXISTS `vpa_personas`;
DROP TABLE IF EXISTS `ai_skill_categories`;
DROP TABLE IF EXISTS `vpa_generation_styles`;
DROP TABLE IF EXISTS `status_definitions`;


-- -----------------------------------------------------
-- 步骤 2: 创建字典表 (无依赖，先行)
-- -----------------------------------------------------

-- 表 1: `ai_skill_categories` - AI技能类别字典表
CREATE TABLE IF NOT EXISTS `ai_skill_categories` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `category_key` VARCHAR(50) NOT NULL COMMENT '类别的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '类别名称，用于UI显示',
  `display_order` INT NOT NULL DEFAULT 0 COMMENT '显示顺序，值越小越靠前',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_category_key_unique` (`category_key`)
) ENGINE=InnoDB COMMENT='AI技能的分类字典表';

-- 表 2: `vpa_generation_styles` - VPA形象生成风格字典表
CREATE TABLE IF NOT EXISTS `vpa_generation_styles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `style_key` VARCHAR(50) NOT NULL COMMENT '风格的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '风格名称，用于UI显示',
  `description` TEXT NULL COMMENT '该风格的详细描述或默认的Prompt提示词',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '该风格当前是否可用',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_style_key_unique` (`style_key`)
) ENGINE=InnoDB COMMENT='VPA形象生成风格字典表';

-- 表 3: `status_definitions` - 通用状态字典表
CREATE TABLE IF NOT EXISTS `status_definitions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `status_key` VARCHAR(50) NOT NULL COMMENT '状态的英文键',
  `scope` VARCHAR(50) NOT NULL COMMENT '作用域 (例如: VPA_INSTANCE)',
  `display_name` VARCHAR(50) NOT NULL COMMENT '状态的中文显示名',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_key_scope_unique` (`status_key`, `scope`)
) ENGINE=InnoDB COMMENT='通用的状态定义字典表';


-- -----------------------------------------------------
-- 步骤 3: 创建核心业务表
-- -----------------------------------------------------

-- 表 4: `ai_skills` - AI技能字典表 (已优化)
CREATE TABLE IF NOT EXISTS `ai_skills` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `skill_key` VARCHAR(50) NOT NULL COMMENT '技能的唯一英文标识 (程序中使用)',
  `category_id` INT NULL COMMENT '逻辑外键, 关联到 ai_skill_categories.id',
  `name` VARCHAR(50) NOT NULL COMMENT '技能名称 (用于列表等短文本场景)',
  `content` JSON NOT NULL COMMENT '技能的完整结构化内容 (包含title, details, cover_image_url, usage_examples等)',
  `unlock_cost_points` INT NOT NULL DEFAULT 0 COMMENT '解锁该技能所需消耗的积分',
  `is_public` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否对所有用户可见',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_skill_key_unique` (`skill_key`)
) ENGINE=InnoDB COMMENT='AI技能字典表，使用JSON字段存储富内容';

-- 表 5: `user_unlocked_skills` - 用户已解锁技能关联表
CREATE TABLE IF NOT EXISTS `user_unlocked_skills` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `skill_id` INT NOT NULL COMMENT '逻辑外键, 关联到 ai_skills.id',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '用户是否开启此技能的主动服务',
  `unlocked_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '技能解锁时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_skill_unique` (`user_id`, `skill_id`)
) ENGINE=InnoDB COMMENT='用户已解锁的技能关联表';

-- 表 6: `vpa_personas` - VPA预设人设字典表
CREATE TABLE IF NOT EXISTS `vpa_personas` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(50) NOT NULL COMMENT '人设名称 (例如: 默认, 后浪)',
  `thumbnail_url` VARCHAR(255) NOT NULL COMMENT '人设的静态缩略图URL',
  `asset_id` VARCHAR(100) NOT NULL COMMENT '关联到远端AI资产库的数字人资产ID',
  `actions` JSON NULL COMMENT '该预设人设拥有的一套基础动作集 (JSON)',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为新用户的默认人设',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB COMMENT='VPA预设人设（形象）字典表';

-- 表 7: `user_vpa_instances` - 用户VPA形象实例表 (核心表)
CREATE TABLE IF NOT EXISTS `user_vpa_instances` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `nickname` VARCHAR(50) NOT NULL COMMENT '用户为该形象实例设置的昵称',
  `status_id` INT NOT NULL COMMENT '逻辑外键, 关联到 status_definitions.id (scope="VPA_INSTANCE")',
  `is_currently_active` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为用户当前正在使用的形象',
  
  -- 资产与动作核心信息
  `asset_id` VARCHAR(100) NOT NULL COMMENT '关联到远端AI资产库的数字人资产ID',
  `actions` JSON NULL COMMENT '该形象实例拥有的一套动作集 (JSON对象, key:动作类型, value:动作资产ID)',
  `thumbnail_url` VARCHAR(255) NOT NULL COMMENT '该实例的静态缩略图URL',
  
  -- 自定义配置
  `custom_wake_word` VARCHAR(20) NULL COMMENT '自定义唤醒词',
  `custom_response` VARCHAR(50) NULL COMMENT '自定义应答语',
  
  -- 生成来源信息 (用于追溯和重新制作)
  `generation_source` ENUM('PRESET', 'UGC') NOT NULL COMMENT '来源 (PRESET:系统预设, UGC:用户生成)',
  `base_persona_id` INT NULL COMMENT '如果来源是PRESET, 关联到 vpa_personas.id',
  `generation_style_id` INT NULL COMMENT '如果来源是UGC, 逻辑外键, 关联到 vpa_generation_styles.id',
  `generation_prompt` TEXT NULL COMMENT '如果来源是UGC, 记录当时用户输入的Prompt',
  
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`)
) ENGINE=InnoDB COMMENT='用户拥有的VPA形象实例表，支持一对多关系';


-- -----------------------------------------------------
-- 步骤 4: 为字典表插入初始数据
-- -----------------------------------------------------

-- 技能分类
INSERT INTO `ai_skill_categories` (`id`, `category_key`, `name`, `display_order`) VALUES
(1, 'family', '亲子家庭', 10),
(2, 'travel', '随车出行', 20),
(3, 'entertainment', '影音娱乐', 30),
(4, 'productivity', '智能助手', 40);

-- VPA生成风格
INSERT INTO `vpa_generation_styles` (`id`, `style_key`, `name`, `description`, `is_available`) VALUES
(1, 'cartoon', '卡通', '生成色彩鲜艳的卡通风格形象。', 1),
(2, 'realistic', '写实', '生成接近真人质感的写实风格形象。', 1),
(3, 'cyberpunk', '赛博朋克', '生成充满未来科技感的赛博朋克风格形象。', 1),
(4, 'fantasy', '奇幻', '生成带有魔法与神话元素的奇幻风格形象。', 0);

-- VPA实例状态
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES
(1, 'ACTIVE', 'VPA_INSTANCE', '可用'),
(2, 'GENERATING', 'VPA_INSTANCE', '制作中'),
(3, 'FAILED', 'VPA_INSTANCE', '制作失败'),
(4, 'ARCHIVED', 'VPA_INSTANCE', '已归档');