<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>V2推送架构测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .step.running {
            border-left-color: #ffc107;
            background-color: #fff3cd;
        }
        .step.success {
            border-left-color: #28a745;
            background-color: #d4edda;
        }
        .step.error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        #events {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f8f9fa;
        }
        .event {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
        .event.step_start {
            background-color: #cce5ff;
        }
        .event.step_end {
            background-color: #ccffcc;
        }
        .event.complete {
            background-color: #ffccff;
        }
        .event.error {
            background-color: #ffcccc;
        }
    </style>
</head>
<body>
    <h1>V2推送架构测试</h1>
    
    <div class="container">
        <h3>测试输入</h3>
        <input type="text" id="query" value="我在福州闽东大厦，这周末要去莆田玩两天" style="width: 100%; padding: 10px;">
        <br><br>
        <button onclick="startTest()">开始测试V2推送架构</button>
        <button onclick="clearEvents()">清空事件</button>
    </div>

    <div class="container">
        <h3>分析步骤</h3>
        <div id="step-core_intent_analysis" class="step">
            <strong>核心意图分析</strong>
            <div class="message">等待开始...</div>
        </div>
        <div id="step-poi_preference_analyzer" class="step">
            <strong>景点偏好分析</strong>
            <div class="message">等待开始...</div>
        </div>
        <div id="step-food_preference_analyzer" class="step">
            <strong>美食偏好分析</strong>
            <div class="message">等待开始...</div>
        </div>
        <div id="step-accommodation_preference_analyzer" class="step">
            <strong>住宿偏好分析</strong>
            <div class="message">等待开始...</div>
        </div>
    </div>

    <div class="container">
        <h3>SSE事件流</h3>
        <div id="events"></div>
    </div>

    <div class="container">
        <h3>最终结果</h3>
        <div id="result">等待结果...</div>
    </div>

    <script>
        let eventSource = null;

        function startTest() {
            const query = document.getElementById('query').value;
            
            // 重置UI
            resetUI();
            
            // 创建SSE连接
            const requestData = {
                user_id: "test_user",
                query: query
            };

            // 使用fetch POST请求启动V2 API
            fetch('/api/travel/v2/plan/stream', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                // 获取响应的reader
                const reader = response.body.getReader();
                const decoder = new TextDecoder();
                
                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            console.log('Stream complete');
                            return;
                        }
                        
                        // 解码数据
                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\n');
                        
                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.substring(6);
                                if (data.trim()) {
                                    try {
                                        const eventData = JSON.parse(data);
                                        handleSSEEvent(eventData);
                                    } catch (e) {
                                        console.error('Failed to parse SSE data:', data, e);
                                    }
                                }
                            }
                        }
                        
                        // 继续读取
                        return readStream();
                    });
                }
                
                return readStream();
            })
            .catch(error => {
                console.error('Error:', error);
                addEvent('error', `连接错误: ${error.message}`);
            });
        }

        function handleSSEEvent(eventData) {
            console.log('收到SSE事件:', eventData);
            
            const { event, data } = eventData;
            
            // 添加到事件日志
            addEvent(event, JSON.stringify(data, null, 2));
            
            switch (event) {
                case 'step_start':
                    updateStepUI(data.step_name, 'running', data.title, data.message);
                    break;
                case 'step_end':
                    updateStepUI(data.step_name, 'success', null, '完成');
                    break;
                case 'complete':
                    document.getElementById('result').innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                    break;
                case 'error':
                    updateStepUI(data.step_name, 'error', null, data.message);
                    break;
                case 'eos':
                    addEvent('eos', '流结束');
                    break;
            }
        }

        function updateStepUI(stepName, status, title, message) {
            const stepElement = document.getElementById(`step-${stepName}`);
            if (stepElement) {
                stepElement.className = `step ${status}`;
                if (title) {
                    stepElement.querySelector('strong').textContent = title;
                }
                if (message) {
                    stepElement.querySelector('.message').textContent = message;
                }
            }
        }

        function addEvent(type, data) {
            const eventsContainer = document.getElementById('events');
            const eventElement = document.createElement('div');
            eventElement.className = `event ${type}`;
            eventElement.innerHTML = `<strong>[${type}]</strong> ${data}`;
            eventsContainer.appendChild(eventElement);
            eventsContainer.scrollTop = eventsContainer.scrollHeight;
        }

        function resetUI() {
            // 重置步骤状态
            const steps = document.querySelectorAll('.step');
            steps.forEach(step => {
                step.className = 'step';
                step.querySelector('.message').textContent = '等待开始...';
            });
            
            // 清空结果
            document.getElementById('result').textContent = '等待结果...';
        }

        function clearEvents() {
            document.getElementById('events').innerHTML = '';
        }
    </script>
</body>
</html>
