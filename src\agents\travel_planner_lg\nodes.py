"""
LangGraph 节点函数实现

每个节点函数负责执行特定的任务步骤，并更新状态。
集成NotificationService实现V2推送架构。
"""
import asyncio
import logging
from typing import Dict, Any
from datetime import datetime
from .state import TravelPlanState, CityPlanResult
from src.services.notification_service import NotificationService
from src.database.redis_client import get_redis_client
from src.core.logger import get_logger

logger = get_logger("travel_planner_nodes")


async def core_intent_analysis_node(state: TravelPlanState) -> TravelPlanState:
    """核心意图分析节点

    解析用户的原始查询，提取目的地、时间、偏好等核心信息。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析核心意图")

    # 获取NotificationService实例
    notification_service = state.get("notification_service")
    task_id = state.get("trace_id")

    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id=task_id,
                step_name="core_intent_analysis",
                title="解析用户需求和画像",
                message="正在分析您的旅行意图..."
            )

        # TODO: 调用分析服务，获得结构化数据
        # core_intent = analysis_service.analyze_core_intent(state['original_query'])

        # 临时实现 - 简单解析
        query = state.get('original_query', '')
        destinations = []

        # 简单的目的地提取逻辑
        if '莆田' in query:
            destinations.append('莆田')
        if '福州' in query:
            destinations.append('福州')
        if '上海' in query:
            destinations.append('上海')
        if '北京' in query:
            destinations.append('北京')
        if '杭州' in query:
            destinations.append('杭州')

        if not destinations:
            destinations = ['莆田']  # 默认目的地

        core_intent = {
            'destinations': destinations,
            'duration_days': 2,  # 从查询中提取的天数
            'travel_type': 'leisure',
            'budget_range': 'medium'
        }

        # 生成旁白文本
        narration_text = f"我理解您想要规划一个{len(destinations)}个城市的旅行，主要目的地包括：{', '.join(destinations)}"

        # 更新状态
        state["destinations"] = destinations
        state["core_intent"] = core_intent
        state["current_narration_text"] = narration_text
        state["current_step"] = "核心意图分析完成"
        state["progress_percentage"] = 25

        # 发送步骤结束事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id=task_id,
                step_name="core_intent_analysis",
                status="success",
                result={"core_intent": core_intent, "narration": narration_text}
            )

        return state

    except Exception as e:
        logger.error(f"核心意图分析失败: {str(e)}")
        if notification_service:
            await notification_service.notify_error(
                task_id=task_id,
                error_message=f"核心意图分析失败: {str(e)}",
                step_name="core_intent_analysis"
            )
        state["error"] = str(e)
        return state


async def poi_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """景点偏好分析节点"""
    logger.info(f"[{state.get('trace_id')}] 开始分析景点偏好")

    notification_service = state.get("notification_service")
    task_id = state.get("trace_id")

    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id=task_id,
                step_name="poi_preference_analyzer",
                title="分析景点偏好",
                message="正在分析您的景点偏好..."
            )

        # 临时实现 - 基于用户查询分析景点偏好
        attraction_prefs = {
            "types": ["文化古迹", "自然风光", "宗教建筑"],
            "crowd_preference": "适中",
            "activity_level": "轻松"
        }

        narration_text = "根据您的偏好，我会为您推荐文化古迹、自然风光和宗教建筑类景点"

        # 更新状态
        state["attraction_preferences"] = attraction_prefs
        state["current_narration_text"] = narration_text
        state["current_step"] = "景点偏好分析完成"
        state["progress_percentage"] = 50

        # 发送步骤结束事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id=task_id,
                step_name="poi_preference_analyzer",
                status="success",
                result={"attraction_preferences": attraction_prefs, "narration": narration_text}
            )

        return state

    except Exception as e:
        logger.error(f"景点偏好分析失败: {str(e)}")
        if notification_service:
            await notification_service.notify_error(
                task_id=task_id,
                error_message=f"景点偏好分析失败: {str(e)}",
                step_name="poi_preference_analyzer"
            )
        state["error"] = str(e)
        return state


async def food_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """美食偏好分析节点"""
    logger.info(f"[{state.get('trace_id')}] 开始分析美食偏好")

    notification_service = state.get("notification_service")
    task_id = state.get("trace_id")

    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id=task_id,
                step_name="food_preference_analyzer",
                title="分析美食偏好",
                message="正在分析您的美食偏好..."
            )

        # 临时实现 - 基于目的地分析美食偏好
        food_prefs = {
            "cuisine_types": ["本地特色", "闽菜", "海鲜"],
            "price_range": "中等",
            "dietary_restrictions": []
        }

        narration_text = "在美食方面，我会特别为您留意本地特色菜和新鲜海鲜"

        # 更新状态
        state["food_preferences"] = food_prefs
        state["current_narration_text"] = narration_text
        state["current_step"] = "美食偏好分析完成"
        state["progress_percentage"] = 75

        # 发送步骤结束事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id=task_id,
                step_name="food_preference_analyzer",
                status="success",
                result={"food_preferences": food_prefs, "narration": narration_text}
            )

        return state

    except Exception as e:
        logger.error(f"美食偏好分析失败: {str(e)}")
        if notification_service:
            await notification_service.notify_error(
                task_id=task_id,
                error_message=f"美食偏好分析失败: {str(e)}",
                step_name="food_preference_analyzer"
            )
        state["error"] = str(e)
        return state


async def accommodation_preference_analyzer_node(state: TravelPlanState) -> TravelPlanState:
    """住宿偏好分析节点"""
    logger.info(f"[{state.get('trace_id')}] 开始分析住宿偏好")

    notification_service = state.get("notification_service")
    task_id = state.get("trace_id")

    try:
        # 发送步骤开始事件
        if notification_service:
            await notification_service.notify_step_start(
                task_id=task_id,
                step_name="accommodation_preference_analyzer",
                title="分析住宿偏好",
                message="正在分析您的住宿偏好..."
            )

        # 临时实现 - 基于自驾需求分析住宿偏好
        accommodation_prefs = {
            "type": "酒店",
            "star_rating": "3-4星",
            "location_preference": "市中心",
            "parking_required": True  # 自驾游必需
        }

        narration_text = "住宿方面，我将为您寻找市中心的3-4星酒店，并且停车无忧"

        # 更新状态
        state["accommodation_preferences"] = accommodation_prefs
        state["current_narration_text"] = narration_text
        state["current_step"] = "住宿偏好分析完成"
        state["progress_percentage"] = 100

        # 发送步骤结束事件
        if notification_service:
            await notification_service.notify_step_end(
                task_id=task_id,
                step_name="accommodation_preference_analyzer",
                status="success",
                result={"accommodation_preferences": accommodation_prefs, "narration": narration_text}
            )

        return state

    except Exception as e:
        logger.error(f"住宿偏好分析失败: {str(e)}")
        if notification_service:
            await notification_service.notify_error(
                task_id=task_id,
                error_message=f"住宿偏好分析失败: {str(e)}",
                step_name="accommodation_preference_analyzer"
            )
        state["error"] = str(e)
        return state


def analyze_multi_city_strategy(state: TravelPlanState) -> Dict[str, Any]:
    """分析多城市策略节点
    
    如果识别出多个目的地，则生成宏观策略。
    在交互模式下，设置clarification_needed标志以暂停并等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析多城市策略")
    
    destinations = state.get("destinations", [])
    
    if len(destinations) > 1:
        # 生成多城市策略
        total_days = state.get("core_intent", {}).get("duration_days", 3)
        days_per_city = max(1, total_days // len(destinations))
        
        strategy = {
            "order": destinations,
            "split": [
                {"city": city, "days": days_per_city} 
                for city in destinations
            ]
        }
        
        narration_text = f"建议您按照 {' -> '.join(destinations)} 的顺序游览，每个城市安排{days_per_city}天"
        
        # 根据模式决定是否需要澄清
        needs_clarification = state.get("execution_mode") == "interactive"
        
        return {
            "multi_city_strategy": strategy,
            "current_narration_text": narration_text,
            "clarification_needed": "multi_city_strategy" if needs_clarification else None,
            "current_step": "多城市策略分析完成",
            "progress_percentage": 30
        }
    
    # 单目的地，直接跳过
    return {
        "current_step": "单目的地，跳过多城市策略",
        "progress_percentage": 30
    }


def analyze_driving_context(state: TravelPlanState) -> Dict[str, Any]:
    """分析驾驶情境节点
    
    分析用户的车辆信息和驾驶需求。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析驾驶情境")
    
    # TODO: 调用用户画像服务获取车辆信息
    # vehicle_info = user_profile_service.get_vehicle_info(state['user_id'])
    
    # 临时实现
    vehicle_info = {
        "model": "Tesla Model Y",
        "nominal_range_km": 450,
        "charge_type": "fast"
    }
    
    # 设定驾驶策略
    if vehicle_info and vehicle_info.get("nominal_range_km"):
        driving_strategy = "range_aware"
        planning_range_km = vehicle_info["nominal_range_km"] * 0.8  # 保守系数
        narration_text = f"检测到您的{vehicle_info['model']}，我会按照续航的80%为您规划路线，确保行程安全"
    else:
        driving_strategy = "general_assistance"
        planning_range_km = None
        narration_text = "我会为您的自驾行程提供停车场和充电站信息"
    
    return {
        "user_vehicle_info": vehicle_info,
        "driving_strategy": driving_strategy,
        "planning_range_km": planning_range_km,
        "range_buffer_factor": 0.8,
        "current_narration_text": narration_text,
        "current_step": "驾驶情境分析完成",
        "progress_percentage": 40
    }


def analyze_preferences(state: TravelPlanState) -> Dict[str, Any]:
    """分析用户偏好节点
    
    整合分析景点、美食、住宿等偏好。
    """
    logger.info(f"[{state.get('trace_id')}] 开始分析用户偏好")
    
    # TODO: 调用分析服务
    # attraction_prefs = analysis_service.analyze_attraction_preferences(...)
    # food_prefs = analysis_service.analyze_food_preferences(...)
    # accommodation_prefs = analysis_service.analyze_accommodation_preferences(...)
    
    # 临时实现
    attraction_prefs = {
        "types": ["文化古迹", "自然风光"],
        "crowd_preference": "适中",
        "activity_level": "轻松"
    }
    
    food_prefs = {
        "cuisine_types": ["本地特色", "川菜"],
        "price_range": "中等",
        "dietary_restrictions": []
    }
    
    accommodation_prefs = {
        "type": "酒店",
        "star_rating": "4星",
        "location_preference": "市中心"
    }
    
    narration_text = "根据您的偏好，我会为您推荐文化古迹和自然风光，安排本地特色美食，选择市中心的4星酒店"
    
    return {
        "attraction_preferences": attraction_prefs,
        "food_preferences": food_prefs,
        "accommodation_preferences": accommodation_prefs,
        "current_narration_text": narration_text,
        "current_step": "用户偏好分析完成",
        "progress_percentage": 50
    }


def wait_for_user_input(state: TravelPlanState) -> Dict[str, Any]:
    """等待用户输入节点
    
    在交互模式下暂停执行，等待用户确认。
    """
    logger.info(f"[{state.get('trace_id')}] 等待用户输入")
    
    # 这个节点主要是标记状态，实际的等待逻辑在图的执行层处理
    return {
        "current_step": "等待用户确认",
        "progress_percentage": state.get("progress_percentage", 50)
    }


def route_after_analysis(state: TravelPlanState) -> str:
    """分析后的路由函数
    
    决定下一步走向：是否需要等待用户输入。
    """
    if state.get("clarification_needed"):
        return "wait_for_user_input"
    else:
        return "continue_analysis"


def decide_planning_or_end(state: TravelPlanState) -> str:
    """决定是继续规划还是结束
    
    在分析的最后阶段决定是继续规划还是结束。
    """
    # 在自动模式下，总是继续
    if state.get("execution_mode") == "automatic":
        return "execute_planning_stage"
    
    # 在交互模式下，根据用户反馈决定
    if state.get("user_feedback") == "proceed":
        return "execute_planning_stage"
    else:
        return "__end__"
