# Agent实时推送架构重构方案 (V2.2)

本文档旨在提出一套基于**事件驱动**和**责任分离**原则的全新实时推送架构，以从根本上解决现有推送机制的耦合问题，并大幅提升系统的健壮性、可维护性和可扩展性。

## 一、问题分析：从表面到根源

### 1. 表面问题
- **SSE推送延迟**：在IO密集型操作（如数据库查询、多工具调用）中，开始和结束事件被一同缓冲，导致前端长时间“假死”。
- **前端体验不佳**：用户无法获得实时的进度反馈，可能因等待时间过长而重复提交请求。

### 2. 架构根源
现有方案（`SSEStreamAdapter`）的核心问题在于**责任不清**和**高度耦合**：

- **业务逻辑泄露 (`Business Logic Leakage`)**: `SSEStreamAdapter`被迫承担了业务逻辑判断的责任。它需要通过检查LangGraph `state`中的字段来“猜测”业务走到了哪一步。
- **高度耦合 (`High Coupling`)**: 推送逻辑与LangGraph的节点名称、状态结构强绑定。一旦业务流程发生变化，`SSEStreamAdapter`必须同步修改，维护成本极高。
- **可复用性差 (`Poor Reusability`)**: 如果要开发新的Agent，当前的`SSEStreamAdapter`几乎无法复用。

## 二、新架构蓝图：解耦、实时、健壮

### 1. 核心组件
- **`NotificationService` (事件发布与状态管理)**: 一个全新的、可复用的服务，负责将业务事件发布到Redis的Pub/Sub频道，**并同步更新Redis中的持久化任务状态**。
- **LangGraph Nodes (业务逻辑单元)**: 在执行关键业务逻辑前后，调用`NotificationService`来宣告“我开始了”或“我结束了”。
- **Redis (消息总线 & 实时作战室)**: 同时作为解耦的**Pub/Sub消息总线**和**HASH状态存储**。
- **API Endpoint (事件订阅与推送者)**: API流式接口订阅Redis频道，接收事件，并将其格式化为SSE推送给前端。

### 2. 数据流转图

```mermaid
sequenceDiagram
    participant FE as Frontend
    participant API as API Endpoint
    participant BG as LangGraph (Background Task)
    participant NS as NotificationService
    participant Redis as Redis Pub/Sub & HASH

    FE->>+API: 发起流式规划请求 (/v2/plan/stream)
    API->>NS: 调用 initialize_task(task_id)
    NS->>Redis: 创建 HASH 'task_status:{task_id}' 并设置TTL
    API->>BG: 启动后台规划任务 (task_id)
    API->>Redis: 订阅频道 (SUBSCRIBE channel:{task_id})
    API-->>-FE: 建立SSE长连接

    loop 规划流程
        BG->>NS: 调用 notify_step_start("解析核心意图", ...)
        NS->>Redis: 更新 HASH 'task_status:{task_id}'
        NS->>Redis: PUBLISH 'task:{task_id}', event_data
        Redis-->>API: 收到 'step_start' 事件
        API-->>FE: 推送SSE step_start 事件

        BG->>NS: 调用 notify_step_end("解析核心意图", ...)
        NS->>Redis: 更新 HASH 'task_status:{task_id}'
        NS->>Redis: PUBLISH 'task:{task_id}', event_data
        Redis-->>API: 收到 'step_end' 事件
        API-->>FE: 推送SSE step_end 事件
    end
```

## 三、任务状态持久化：Redis实时作战室

为了实现系统的可观测性、可恢复性和健壮性，除了Pub/Sub消息外，我们必须在Redis中为每个任务维护一个持久化的状态记录。

### 1. 数据结构：Redis HASH

-   **Key**: `task_status:{task_id}` (例如: `task_status:task_guest_1688888888`)
-   **Type**: `HASH`

| Field | Type | Description | Example |
| :--- | :--- | :--- | :--- |
| `overall_status` | string | 整个任务的宏观状态。枚举值: `pending`, `running`, `completed`, `failed` | `running` |
| `current_step` | string | 当前正在执行的步骤的 `step_name` | `core_intent_analysis` |
| `steps_status` | JSON string | 一个记录所有步骤详细状态的JSON对象。 | `{"core_intent_analysis": {"status": "running", ...}}` |
| `last_updated` | string | 状态最后更新的ISO 8601时间戳 | `2023-07-09T12:30:05.123Z` |
| `final_result` | JSON string | 任务成功完成后，存储最终的结构化结果。 | `{"itinerary": {"title": "..."}}` |
| `error_info` | string | 任务失败时，存储详细的错误信息。 | `LLM service timeout` |


### 2. 状态消费者 (谁来监听?)

-   **实时事件监听 (SSE推送)**: **API流式接口** 监听 **Pub/Sub频道**，用于实时驱动前端UI更新。
-   **任务状态监控 (按需读取)**:
    -   **运维监控平台**: 可开发内部后台，读取 **HASH数据**，实现对系统所有任务健康状况的**可观测性**。
    -   **开发/支持人员**: 可通过 `HGETALL` 命令直接查看指定任务的最终状态，用于**问题追溯和调试**。
    -   **未来的任务恢复服务**: 可构建后台进程，扫描卡死的 `running` 任务并进行清理，实现系统**自愈能力**。

### 3. 任务超时与TTL

为防止“僵尸数据”占满内存，必须为任务状态设置TTL。
-   **设置目标**: `task_status:{task_id}` 这个 `HASH` 主键。
-   **设置时机**: 任务创建初始化时。
-   **推荐时长**: **1小时 (3600秒)**，应在应用配置中可调。


## 四、固定的SSE事件格式定义
所有通过SSE推送的事件都必须严格遵守以下JSON结构。

*   **步骤开始 (`step_start`)**:
    ```json
    { "event": "step_start", "data": { "step_id": "...", "step_name": "...", "title": "...", "message": "..." } }
    ```
*   **步骤结束 (`step_end`)**:
    ```json
    { "event": "step_end", "data": { "step_id": "...", "step_name": "...", "status": "success", "result": { "content": "..." } } }
    ```
*   **规划完成 (`complete`)**:
    ```json
    { "event": "complete", "data": { "itinerary": { ... } } }
    ```
*   **发生错误 (`error`)**:
    ```json
    { "event": "error", "data": { "step_name": "...", "message": "..." } }
    ```
*   **流结束信号 (`eos`)**:
    ```json
    { "event": "eos" }
    ```

## 五、实施路径图与核心代码

**核心战略**: **完全迁移到 `src/agents/travel_planner_lg` 的简洁架构**，并彻底删除整个 `src/agents/travel_planner_langgraph` 目录。

### 1. **创建并迁移 `NotificationService`**
-   **位置**: `src/services/notification_service.py`
-   **任务**: 实现一个包含**状态管理**和**事件发布**双重职责的增强版服务。
-   **代码实现**:
    ```python
    # src/services/notification_service.py
    import json
    import logging
    from datetime import datetime
    from typing import Dict, Any, Optional

    from src.database.redis_client import RedisClient

    logger = logging.getLogger(__name__)

    class NotificationService:
        def __init__(self, redis_client: RedisClient, task_ttl: int = 3600):
            self.redis = redis_client.client
            self.task_ttl = task_ttl

        async def _publish(self, task_id: str, event_data: Dict[str, Any]):
            channel = f"task_channel:{task_id}"
            message = json.dumps(event_data, ensure_ascii=False)
            await self.redis.publish(channel, message)

        async def _update_task_status(self, task_id: str, updates: Dict[str, Any]):
            key = f"task_status:{task_id}"
            updates["last_updated"] = datetime.utcnow().isoformat()
            await self.redis.hset(key, mapping=updates)
            # 每次更新都刷新TTL，防止正常任务过期
            await self.redis.expire(key, self.task_ttl)

        async def initialize_task(self, task_id: str):
            """初始化任务状态并设置TTL。"""
            initial_state = {
                "overall_status": "pending",
                "current_step": "initialization",
                "steps_status": json.dumps({})
            }
            await self._update_task_status(task_id, initial_state)
            logger.info(f"Task {task_id} state initialized with TTL: {self.task_ttl}s.")

        async def notify_step_start(self, task_id: str, step_name: str, title: str, message: str):
            event = {"event": "step_start", "data": {"step_id": f"{step_name}_{task_id}", "step_name": step_name, "title": title, "message": message}}
            await self._publish(task_id, event)
            
            status_update = {
                "overall_status": "running",
                "current_step": step_name
            }
            # 更新步骤状态
            key = f"task_status:{task_id}"
            steps_status_raw = await self.redis.hget(key, "steps_status")
            steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
            steps_status[step_name] = {"status": "running", "start_time": datetime.utcnow().isoformat()}
            status_update["steps_status"] = json.dumps(steps_status)
            
            await self._update_task_status(task_id, status_update)

        async def notify_step_end(self, task_id: str, step_name: str, status: str, result: Dict[str, Any] = None):
            event = {"event": "step_end", "data": {"step_id": f"{step_name}_{task_id}", "step_name": step_name, "status": status, "result": result or {}}}
            await self._publish(task_id, event)
            
            key = f"task_status:{task_id}"
            steps_status_raw = await self.redis.hget(key, "steps_status")
            steps_status = json.loads(steps_status_raw) if steps_status_raw else {}
            if step_name in steps_status:
                steps_status[step_name]["status"] = status
                steps_status[step_name]["end_time"] = datetime.utcnow().isoformat()
            
            await self._update_task_status(task_id, {"steps_status": json.dumps(steps_status)})

        async def notify_final_result(self, task_id: str, final_data: Dict[str, Any]):
            event = {"event": "complete", "data": final_data}
            await self._publish(task_id, event)
            await self._publish(task_id, {"event": "eos"})
            
            status_update = {
                "overall_status": "completed",
                "current_step": "finished",
                "final_result": json.dumps(final_data)
            }
            await self._update_task_status(task_id, status_update)

        async def notify_error(self, task_id: str, error_message: str, step_name: str = "unknown"):
            event = {"event": "error", "data": {"step_name": step_name, "message": error_message}}
            await self._publish(task_id, event)
            await self._publish(task_id, {"event": "eos"})
            
            status_update = {
                "overall_status": "failed",
                "current_step": step_name,
                "error_info": error_message
            }
            await self._update_task_status(task_id, status_update)
    ```

### 2. **改造 `travel_planner_lg` 节点**
-   **位置**: `src/agents/travel_planner_lg/nodes.py`
-   **任务**: 为所有节点函数包裹上通知调用，并实现健壮的错误处理。

### 3. **实现新API端点**
-   **位置**: `src/api/travel_planner.py`
-   **任务**: 添加新的`/v2/plan/stream`端点，并在任务启动前调用`initialize_task`。
-   **代码实现**:
    ```python
    # src/api/travel_planner.py
    import asyncio
    import json
    import time
    from fastapi import APIRouter, Depends, Request, BackgroundTasks
    from sse_starlette.sse import EventSourceResponse
    from src.core.config import settings # 假设从统一配置入口获取TTL
    from src.database.redis_client import RedisClient, get_redis_client
    from src.services.notification_service import NotificationService
    from src.agents import NewTravelPlannerAgent # 指向 travel_planner_lg 的新版Agent

    router = APIRouter()

    async def redis_event_generator(task_id: str, redis_client: RedisClient):
        """监听Redis频道并生成SSE事件"""
        channel = f"task_channel:{task_id}"
        pubsub = redis_client.client.pubsub()
        await pubsub.subscribe(channel)
        try:
            while True:
                message = await pubsub.get_message(ignore_subscribe_messages=True, timeout=60)
                if message and message['data']:
                    data = message['data'].decode('utf-8')
                    yield f"data: {data}\n\n"
                    if '"event": "eos"' in data:
                        break
                await asyncio.sleep(0.01)
        finally:
            await pubsub.unsubscribe(channel)

    @router.post("/v2/plan/stream", name="流式旅行规划 V2")
    async def plan_travel_stream_v2(
        request_data: dict,
        background_tasks: BackgroundTasks,
        redis_client: RedisClient = Depends(get_redis_client)
    ):
        """V2版流式规划接口，基于Redis Pub/Sub实现"""
        user_id = request_data.get("user_id", "guest")
        query = request_data.get("query")
        task_id = f"task_{user_id}_{int(time.time())}"

        # 从配置中心读取TTL，增强灵活性
        task_ttl = settings.REDIS_TASK_TTL or 3600
        notification_service = NotificationService(redis_client, task_ttl=task_ttl)
        
        # 关键一步: 在启动后台任务前，先初始化Redis中的状态并设置TTL
        await notification_service.initialize_task(task_id)

        agent = NewTravelPlannerAgent()
        background_tasks.add_task(
            agent.run,
            query=query, user_id=user_id, session_id=task_id,
            config={"notification_service": notification_service}
        )
        
        return EventSourceResponse(redis_event_generator(task_id, redis_client))

    ```

### 4. **废弃旧代码**
-   **目标**: `src/agents/travel_planner_langgraph/stream_adapter.py` 和整个 `travel_planner_langgraph` 目录。
-   **任务**: 一旦新API及相关流程验证通过，可安全地删除这些旧文件。

## 六、前端适配指南 (V2.2)

### 1. 核心原则：事件驱动UI
-   前端UI的每一个“卡片”或“步骤”都应有一个固定的HTML `id`，与后端事件清单中的`step_name`相对应。例如：`id="step-card-core_intent_analysis"`。
-   JS逻辑通过SSE事件中的 `step_name` 字段找到对应的HTML元素，并根据事件类型 (`step_start`, `step_end`) 和 `status` 更新其CSS类和内容。

### 2. SSE事件处理改造
-   **位置**: `static/js/app-refactored.js`
-   **代码实现**:
    ```javascript
    // static/js/app-refactored.js
    
    class TravelPlannerApp {
        constructor() {
            this.eventSource = null;
            this.planButton = document.getElementById('plan-button');
            this.userInput = document.getElementById('user-input');
            this.itineraryContainer = document.getElementById('itinerary-container');
        }

        startPlanning() {
            if (this.eventSource) return; // 防止重复点击

            this.planButton.disabled = true;
            this.planButton.textContent = '规划中...';
            this.resetUI();

            const query = this.userInput.value;
            // 真实应用中，用fetch POST启动任务，用返回的task_id建立EventSource连接
            // 此处简化为直接请求V2 API
            this.eventSource = new EventSource(`/api/travel_planner/v2/plan/stream?query=${encodeURIComponent(query)}`); // 修正为正确的API路径

            this.eventSource.onmessage = (event) => this.handleSSEEvent(JSON.parse(event.data));
            this.eventSource.onerror = () => this.handleError("与服务器的实时连接中断，请刷新重试。");
        }
        
        handleSSEEvent(eventData) {
            const { event, data } = eventData;

            switch (event) {
                case 'step_start':
                    this.updateStepUI(data.step_name, 'in-progress', data.title, data.message);
                    break;
                case 'step_end':
                    this.updateStepUI(data.step_name, data.status, null, data.result?.content || '已完成');
                    break;
                case 'complete':
                    this.renderFinalItinerary(data.itinerary);
                    break;
                case 'error':
                    this.handleError(data.message);
                    break;
                case 'eos':
                    this.finishPlanning();
                    break;
            }
        }

        updateStepUI(stepName, status, title, message) {
            const stepCard = document.getElementById(`step-card-${stepName}`);
            if (!stepCard) return;

            stepCard.className = `step-card ${status}`;
            if (title) stepCard.querySelector('.step-title').textContent = title;
            if (message) stepCard.querySelector('.step-message').textContent = message;
        }
        
        renderFinalItinerary(itinerary) {
            // ... 在这里将最终的行程渲染到 this.itineraryContainer ...
            this.itineraryContainer.innerHTML = `<pre>${JSON.stringify(itinerary, null, 2)}</pre>`;
        }
        
        handleError(errorMessage) {
            alert(`发生错误: ${errorMessage}`);
            this.finishPlanning();
        }

        finishPlanning() {
            if (this.eventSource) {
                this.eventSource.close();
                this.eventSource = null;
            }
            this.planButton.disabled = false;
            this.planButton.textContent = '开始规划';
        }
        
        resetUI() {
            // ... 重置所有步骤卡片到初始状态 ...
            document.querySelectorAll('.step-card').forEach(card => {
                card.className = 'step-card hidden';
                card.querySelector('.step-message').textContent = '等待开始...';
            });
            this.itineraryContainer.innerHTML = '';
        }
    }

    // 绑定事件
    const app = new TravelPlannerApp();
    app.planButton.addEventListener('click', () => app.startPlanning());
    ```

## 七、分阶段测试与验证策略 (V2.3)

任何没有测试的重构都是不可靠的。为确保新架构的健壮性，我们必须采用与开发并行的分阶段测试策略。

### 1. 测试总览

- **测试金字塔**: 我们将遵循标准的测试金字塔模型，包含大量的单元测试、适量的集成测试和少量的端到端测试。
- **自动化优先**: 尽可能编写自动化的测试脚本，减少手动回归测试的成本。
- **位置**: 所有新的测试脚本都应放在 `tests/` 目录下，并遵循清晰的命名规范，例如 `tests/unit/services/test_notification_service.py`。

### 2. 阶段一：单元测试 (Unit Testing)

**目标**: 独立验证 `NotificationService` 的所有公共方法的逻辑正确性。

-   **测试对象**: `src/services/notification_service.py`
-   **关键技术**: 使用 `pytest` 和 `pytest-asyncio`，并**模拟(mock)** Redis客户端，以避免真实的网络I/O。
-   **测试用例**:
    -   **`test_initialize_task`**:
        -   **验证**: 调用后，模拟的redis客户端 `hset` 和 `expire` 方法是否被以正确的参数（key, mapping, ttl）调用。
        -   **验证**: `overall_status` 是否为 `pending`。
    -   **`test_notify_step_start`**:
        -   **验证**: `publish` 方法是否被调用，且发布的消息符合 `step_start` 事件的JSON格式。
        -   **验证**: `hget` 和 `hset` 是否被调用来更新 `steps_status` JSON。
        -   **验证**: `overall_status` 是否更新为 `running`，`current_step` 是否为当前步骤名。
    -   **`test_notify_step_end`**:
        -   **验证**: `publish` 方法是否被调用，且发布的消息符合 `step_end` 事件的JSON格式。
        -   **验证**: `hget` 和 `hset` 是否被调用以更新对应步骤的状态为 `success` 或 `failed`。
    -   **`test_notify_final_result`**:
        -   **验证**: 是否连续发布了 `complete` 和 `eos` 两个事件。
        -   **验证**: `overall_status` 是否更新为 `completed`，并且 `final_result` 字段被正确写入。
    -   **`test_notify_error`**:
        -   **验证**: 是否连续发布了 `error` 和 `eos` 两个事件。
        -   **验证**: `overall_status` 是否更新为 `failed`，并且 `error_info` 字段被正确写入。

### 3. 阶段二：集成测试 (Integration Testing)

**目标**: 验证重构后的组件之间能否正确协作。

-   **Part A: API层集成测试**
    -   **测试对象**: `src/api/travel_planner.py` 中的 `/v2/plan/stream` 端点。
    -   **测试方法**: 使用 `TestClient`。
    -   **测试用例**:
        -   **验证**: POST请求到该端点时，`NotificationService.initialize_task` 方法是否被调用。
        -   **验证**: FastAPI 的 `BackgroundTasks.add_task` 是否被以正确的参数调用。
        -   **验证**: API的响应是否为 `EventSourceResponse` 类型，并且状态码为 200。
-   **Part B: Agent节点集成测试**
    -   **测试对象**: `src/agents/travel_planner_lg/nodes.py` 中的一个或多个节点。
    -   **测试方法**: 直接调用节点函数。
    -   **测试用例**:
        -   **验证**: 在调用节点函数时，传入一个**模拟的 `NotificationService` 实例**。
        -   **验证**: 节点执行成功后，断言模拟服务上的 `notify_step_start` 和 `notify_step_end` 方法被正确调用。
        -   **验证**: 如果节点执行期间发生异常，断言 `notify_error` 方法被调用。

### 4. 阶段三：端到端（E2E）测试

**目标**: 验证从用户请求到收到完整规划结果的整个业务流程，即您提到的“**全部节点串联测试**”。

-   **测试脚本**: `tests/integration/test_travel_planner_v2_e2e.py`
-   **环境要求**: 需要一个**正在运行的、包含所有服务的应用实例**，以及一个可用的Redis。
-   **测试流程**:
    1.  脚本启动，向真实的 `/v2/plan/stream` API端点发送一个POST请求（例如，规划“周末去北京”）。
    2.  脚本作为一个SSE客户端，持续接收并收集所有事件。
    3.  **断言与验证**:
        -   **事件流验证**: 检查接收到的事件序列是否符合业务逻辑（`intent_start` -> `intent_end` -> `attraction_start` -> ...）。
        -   **内容验证**: 检查 `step_end` 事件中的结果是否有意义。
        -   **完整性验证**: 检查最后是否收到了 `complete` 和 `eos` 事件。
        -   **Redis状态验证**: 在 `eos` 事件后，脚本连接到Redis，读取 `task_status:{task_id}` HASH，验证 `overall_status` 是否为 `completed`，以及 `final_result` 是否包含有效的行程数据。

### 5. 阶段四：手动与前端验证

**目标**: 确保最终用户体验符合预期。

-   **执行者**: 开发人员或QA测试人员。
-   **流程**:
    1.  启动完整的Web应用。
    2.  打开浏览器并访问 `http://localhost:port/static/index.html`。
    3.  **UI交互测试**:
        -   在输入框中输入一个规划请求，点击“开始规划”按钮。
        -   **视觉验证**: 观察界面上的步骤卡片是否按顺序出现，并正确地从“分析中...”状态变为“完成”状态。
        -   **最终结果验证**: 检查最终生成的行程卡片是否正确显示。
    4.  **开发者工具检查**:
        -   打开浏览器的开发者工具（F12），切换到“网络(Network)”面板，检查SSE连接是否稳定。
        -   切换到“控制台(Console)”面板，检查是否有任何JavaScript错误。

## 八、文件修改清单 (V2.3)

本章节提供一份在开发过程中需要修改、创建或删除的文件的完整清单，作为具体编码工作的实施指南。

### 1. 后端 (`src` 目录)

| 文件路径 | 操作 | 核心修改内容 |
| :--- | :--- | :--- |
| `src/services/notification_service.py` | **新建** | 实现文档第五章定义的 `NotificationService` 完整类，负责事件发布和Redis HASH状态管理。 |
| `src/api/travel_planner.py` | **修改** | - **新增** `/v2/plan/stream` API端点。<br>- 在端点中注入 `NotificationService` 和 `BackgroundTasks`。<br>- 在启动后台任务前，调用 `notification_service.initialize_task()`。<br>- 实现 `redis_event_generator` 以监听Redis Pub/Sub并生成SSE事件。 |
| `src/agents/travel_planner_lg/nodes.py` | **修改** | - **重构所有节点函数**：使其能够从`state`中接收`notification_service`实例。<br>- **包裹核心逻辑**：在每个节点中使用`try...except...`结构。<br>- **调用通知**：在`try`块开始时调用`notify_step_start`，在成功后调用`notify_step_end`，在`except`块中调用`notify_error`。 |
| `src/agents/travel_planner_lg/graph.py` | **修改** | 确保在编译和调用LangGraph时，`notification_service`实例能被正确地注入到初始`state`中。 |
| `src/core/config.py` & `config/default.yaml` | **修改** | - 在`default.yaml`中**添加**新的配置项，如`TASK_TTL_SECONDS`、`REDIS_KEY_PREFIXES`等。<br>- 在`config.py`中的Pydantic模型中**定义**相应的字段来加载这些配置。 |
| `src/agents/travel_planner_langgraph/` (整个目录) | **删除** | 在V2版本API完全稳定并通过所有测试后，**安全地删除**这个包含了旧版`SSEStreamAdapter`和相关逻辑的整个目录，以完成技术债务清理。 |

### 2. 前端 (`static` 目录)

| 文件路径 | 操作 | 核心修改内容 |
| :--- | :--- | :--- |
| `static/js/app-refactored.js` (或新建) | **修改** | - **重构`EventSource`逻辑**：使其请求新的`/v2/plan/stream`端点。<br>- **实现`handleSSEEvent`**：用于解析和分发`step_start`, `step_end`, `complete`, `error`, `eos`等标准事件。<br>- **实现`updateStepUI`**：根据事件中的`step_name`找到对应的HTML元素，并更新其UI状态（CSS类、文本内容等）。 |
| `static/index.html` | **修改** | - **创建步骤卡片容器**：确保所有步骤UI卡片都有一个父容器。<br>- **定义步骤卡片**：为后端工作流中的每一个`step_name`，创建一个对应的HTML元素（如`<div>`），并为其分配一个唯一的ID，例如`id="step-card-core_intent_analysis"`，以便JS可以精确地找到并更新它。 |

### 3. 测试 (`tests` 目录)

| 文件路径 | 操作 | 核心修改内容 |
| :--- | :--- | :--- |
| `tests/unit/services/test_notification_service.py` | **新建** | 编写针对 `NotificationService` 的单元测试，使用`pytest-asyncio`并模拟Redis客户端。 |
| `tests/integration/test_travel_planner_api_v2.py` | **新建** | 编写针对新API端点的集成测试，验证其能否正确初始化任务和启动后台Agent。 |
| `tests/integration/test_travel_planner_v2_e2e.py` | **新建** | 编写完整的端到端测试，模拟客户端请求并验证整个SSE事件流和最终的Redis状态。 |
