"""
TravelPlannerAgent LangGraph工作流图 (V2推送架构)

使用LangGraph构建的旅行规划Agent工作流，集成V2推送架构。
支持四个独立分析节点的并行执行和统一SSE事件流。
"""

import logging
import time
from typing import Dict, Any, List, Optional
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import TravelPlanState
from .nodes import (
    core_intent_analysis_node,
    poi_preference_analyzer_node,
    food_preference_analyzer_node,
    accommodation_preference_analyzer_node
)
from src.services.notification_service import NotificationService
from src.database.redis_client import get_redis_client

logger = logging.getLogger(__name__)


class TravelPlannerGraph:
    """
    旅行规划Agent图形类 (V2推送架构)

    集成V2推送架构，支持：
    1. Redis Pub/Sub事件驱动
    2. 四个独立分析节点的SSE事件流
    3. 统一的任务状态管理
    4. 前端等待所有分析完成的逻辑
    """

    def __init__(self):
        """初始化图形"""
        self.graph = None
        self.compiled_graph = None
        self.checkpointer = MemorySaver()
        self._build_graph()
    
    def _build_graph(self):
        """构建V2推送架构的工作流图"""
        logger.info("开始构建TravelPlannerAgent V2推送工作流图")

        # 创建状态图
        workflow = StateGraph(TravelPlanState)

        # 添加分析节点
        workflow.add_node("core_intent_analysis", core_intent_analysis_node)
        workflow.add_node("poi_preference_analyzer", poi_preference_analyzer_node)
        workflow.add_node("food_preference_analyzer", food_preference_analyzer_node)
        workflow.add_node("accommodation_preference_analyzer", accommodation_preference_analyzer_node)

        # 设置入口点
        workflow.set_entry_point("core_intent_analysis")

        # 添加边 - 顺序执行四个分析节点
        workflow.add_edge("core_intent_analysis", "poi_preference_analyzer")
        workflow.add_edge("poi_preference_analyzer", "food_preference_analyzer")
        workflow.add_edge("food_preference_analyzer", "accommodation_preference_analyzer")
        workflow.add_edge("accommodation_preference_analyzer", END)

        # 编译图
        self.compiled_graph = workflow.compile(checkpointer=self.checkpointer)
        logger.info("TravelPlannerAgent V2推送工作流图构建完成")

    async def run_analysis_phase(
        self,
        user_id: str,
        query: str,
        session_id: str,
        notification_service: NotificationService
    ) -> Dict[str, Any]:
        """
        运行分析阶段

        Args:
            user_id: 用户ID
            query: 用户查询
            session_id: 会话ID
            notification_service: 通知服务实例

        Returns:
            分析结果状态
        """
        try:
            logger.info(f"开始运行分析阶段 - Session: {session_id}")

            # 创建初始状态
            initial_state = {
                "trace_id": session_id,
                "user_id": user_id,
                "original_query": query,
                "execution_mode": "interactive",
                "destinations": [],
                "progress_percentage": 0,
                "current_step": "初始化",
                "notification_service": notification_service
            }

            # 运行工作流
            config = {"configurable": {"thread_id": session_id}}

            final_state = await self.compiled_graph.ainvoke(
                initial_state,
                config=config
            )

            logger.info(f"分析阶段完成 - Session: {session_id}")
            return final_state

        except Exception as e:
            logger.error(f"分析阶段执行失败 - Session: {session_id}, Error: {str(e)}")
            if notification_service:
                await notification_service.notify_error(
                    task_id=session_id,
                    error_message=f"分析阶段执行失败: {str(e)}",
                    step_name="analysis_phase"
                )
            raise

    async def run_planning_phase(
        self,
        session_id: str,
        analysis_state: Dict[str, Any],
        notification_service: NotificationService
    ) -> Dict[str, Any]:
        """
        运行规划阶段

        Args:
            session_id: 会话ID
            analysis_state: 分析阶段的状态
            notification_service: 通知服务实例

        Returns:
            规划结果
        """
        try:
            logger.info(f"开始运行规划阶段 - Session: {session_id}")

            # 发送规划开始事件
            await notification_service.notify_step_start(
                task_id=session_id,
                step_name="planning_phase",
                title="开始规划您的精彩行程",
                message="正在为您生成详细的旅行行程..."
            )

            # TODO: 实现实际的规划逻辑
            # 这里应该调用POI查询、路线优化等服务
            
            # 临时实现 - 生成示例行程
            destinations = analysis_state.get("destinations", ["莆田"])
            core_intent = analysis_state.get("core_intent", {})
            
            itinerary = {
                "title": f"{', '.join(destinations)}自驾游",
                "duration": core_intent.get("duration_days", 2),
                "destinations": destinations,
                "daily_plans": [
                    {
                        "day": 1,
                        "theme": "DAY1探索之旅",
                        "locations": [
                            {
                                "name": "湄洲岛",
                                "type": "景点",
                                "description": "妈祖文化发源地，海上仙境",
                                "image": "/static/images/meizhou-island.jpg",
                                "time": "09:00-12:00"
                            },
                            {
                                "name": "莆田荔枝",
                                "type": "美食",
                                "description": "当地特色水果，甜美可口",
                                "image": "/static/images/putian-lychee.jpg",
                                "time": "12:30-13:30"
                            }
                        ]
                    },
                    {
                        "day": 2,
                        "theme": "DAY2文化之旅",
                        "locations": [
                            {
                                "name": "南少林寺",
                                "type": "景点",
                                "description": "千年古刹，武术圣地",
                                "image": "/static/images/nanshao-temple.jpg",
                                "time": "09:00-11:30"
                            }
                        ]
                    }
                ]
            }

            # 发送规划完成事件
            await notification_service.notify_final_result(
                task_id=session_id,
                final_data={"itinerary": itinerary}
            )

            logger.info(f"规划阶段完成 - Session: {session_id}")
            return {"itinerary": itinerary}

        except Exception as e:
            logger.error(f"规划阶段执行失败 - Session: {session_id}, Error: {str(e)}")
            await notification_service.notify_error(
                task_id=session_id,
                error_message=f"规划阶段执行失败: {str(e)}",
                step_name="planning_phase"
            )
            raise
